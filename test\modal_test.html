<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模態視窗測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .bookignPeriodGroup {
            border: 1px solid #ccc;
            margin: 10px 0;
            padding: 10px;
        }
        .quick-input-button-container {
            margin: 10px 0;
        }
        .btn {
            padding: 5px 10px;
            margin: 2px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-sm {
            font-size: 12px;
            padding: 3px 8px;
        }
        input.bookingtitle {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>模態視窗功能測試</h1>
        
        <div class="test-section">
            <h2>測試 1: 模態視窗顯示控制</h2>
            <p>這個測試驗證模態視窗只在點擊「編輯快速按鈕」時才顯示</p>
            <button class="test-button" onclick="testModalDisplay()">測試模態視窗顯示控制</button>
            <div id="test1-result"></div>
        </div>

        <div class="test-section">
            <h2>測試 2: 關閉功能</h2>
            <p>這個測試驗證點擊 X 按鈕能正常關閉模態視窗</p>
            <button class="test-button" onclick="testModalClose()">測試模態視窗關閉功能</button>
            <div id="test2-result"></div>
        </div>

        <div class="test-section">
            <h2>模擬 VTC iBooking 環境</h2>
            <p>模擬實際的預訂頁面環境</p>
            <div class="bookignPeriodGroup">
                <div style="background-color:#e4ece0; padding: 10px;">
                    Meeting Name 會議名稱
                    <!-- 快速按鈕會被動態插入到這裡 -->
                </div>
                <input type="text" class="bookingtitle" placeholder="輸入會議標題">
            </div>
        </div>

        <div class="test-section">
            <h2>測試結果</h2>
            <div id="overall-result"></div>
        </div>
    </div>

    <script>
        // 載入修復後的腳本（模擬部分功能）
        let testResults = [];

        function testModalDisplay() {
            const result = document.getElementById('test1-result');
            
            // 檢查模態視窗是否在頁面載入時就存在
            const modalExists = document.getElementById('quick-button-settings-modal');
            
            if (!modalExists) {
                result.innerHTML = '<span style="color: green;">✓ 通過：模態視窗在初始化時不存在</span>';
                testResults.push('test1: PASS');
            } else {
                const isVisible = modalExists.style.display !== 'none';
                if (isVisible) {
                    result.innerHTML = '<span style="color: red;">✗ 失敗：模態視窗在初始化時就顯示</span>';
                    testResults.push('test1: FAIL');
                } else {
                    result.innerHTML = '<span style="color: green;">✓ 通過：模態視窗存在但隱藏</span>';
                    testResults.push('test1: PASS');
                }
            }
            updateOverallResult();
        }

        function testModalClose() {
            const result = document.getElementById('test2-result');
            
            // 模擬創建模態視窗
            if (!document.getElementById('quick-button-settings-modal')) {
                createTestModal();
            }
            
            const modal = document.getElementById('quick-button-settings-modal');
            const closeBtn = modal.querySelector('.qbs-modal-close');
            
            if (closeBtn) {
                // 顯示模態視窗
                modal.style.display = 'flex';
                
                // 模擬點擊關閉按鈕
                closeBtn.click();
                
                setTimeout(() => {
                    if (modal.style.display === 'none') {
                        result.innerHTML = '<span style="color: green;">✓ 通過：點擊 X 按鈕能正常關閉模態視窗</span>';
                        testResults.push('test2: PASS');
                    } else {
                        result.innerHTML = '<span style="color: red;">✗ 失敗：點擊 X 按鈕無法關閉模態視窗</span>';
                        testResults.push('test2: FAIL');
                    }
                    updateOverallResult();
                }, 100);
            } else {
                result.innerHTML = '<span style="color: red;">✗ 失敗：找不到關閉按鈕</span>';
                testResults.push('test2: FAIL');
                updateOverallResult();
            }
        }

        function createTestModal() {
            const modalHTML = `
                <div id="quick-button-settings-modal" class="qbs-modal-overlay" style="display: none;">
                    <div class="qbs-modal-box">
                        <div class="qbs-modal-header">
                            <h2><span style="color:#007bff;">⚡</span> 快速輸入按鈕設定</h2>
                            <span class="qbs-modal-close" title="關閉">×</span>
                        </div>
                        <div class="qbs-modal-body"><div id="qbs-config-list"></div></div>
                        <div class="qbs-modal-footer">
                            <button id="qbs-add-new-btn" class="qbs-modal-btn qbs-modal-btn-secondary">＋ 新增</button>
                            <button id="qbs-save-btn" class="qbs-modal-btn qbs-modal-btn-primary">💾 儲存並關閉</button>
                        </div>
                    </div>
                </div>`;
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            
            // 綁定關閉事件
            const modal = document.getElementById('quick-button-settings-modal');
            const closeBtn = modal.querySelector('.qbs-modal-close');
            if (closeBtn) {
                closeBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    modal.style.display = 'none';
                });
            }
        }

        function updateOverallResult() {
            const overallResult = document.getElementById('overall-result');
            const passCount = testResults.filter(r => r.includes('PASS')).length;
            const totalTests = testResults.length;
            
            if (totalTests === 0) {
                overallResult.innerHTML = '<p>尚未執行測試</p>';
            } else if (passCount === totalTests) {
                overallResult.innerHTML = `<p style="color: green; font-weight: bold;">所有測試通過 (${passCount}/${totalTests})</p>`;
            } else {
                overallResult.innerHTML = `<p style="color: orange; font-weight: bold;">部分測試通過 (${passCount}/${totalTests})</p>`;
            }
        }

        // 頁面載入時執行初始檢查
        window.addEventListener('load', () => {
            console.log('測試頁面載入完成');
        });
    </script>
</body>
</html>
