// ==UserScript==
// @name         VTC iBooking - Customizable Quick Input Buttons & Full Screen
// @namespace    http://tampermonkey.net/
// @version      6.1
// @description  The ultimate version: All layout fixes, plus a modern UI to customize quick input buttons stored in your browser. (Event binding fixed)
// @description:zh-TW 終極版本：包含所有佈局修復，並提供一個現代化的設定介面來自訂快速輸入按鈕。(已修復事件綁定問題)
// <AUTHOR> for you)
// @match        https://ibooking.vtc.edu.hk/*
// @icon         https://www.google.com/s2/favicons?sz=64&domain=edu.hk
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    let customStyleSheet = null;
    const STORAGE_KEY = 'vtc_ibooking_quick_buttons_config';

    // --- Settings Management ---
    function getButtonConfig() {
        const storedConfig = localStorage.getItem(STORAGE_KEY);
        if (storedConfig) {
            try { return JSON.parse(storedConfig); } catch (e) { console.error("Error parsing stored config:", e); }
        }
        return [{ label: '電話', text: ' (電話會議)' }, { label: '實體', text: ' (實體會議)' }, { label: '面試', text: ' (面試)' }];
    }

    function saveButtonConfig(config) {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(config));
    }

    function createSettingsModal() {
        if (document.getElementById('quick-button-settings-modal')) {
            console.log('[Modal] createSettingsModal: Modal already exists');
            return;
        }

        console.log('[Modal] createSettingsModal: Creating modal');
        const modalHTML = `
            <div id="quick-button-settings-modal" class="qbs-modal-overlay" style="display: none;">
                <div class="qbs-modal-box">
                    <div class="qbs-modal-header">
                        <h2><span style="color:#007bff;">⚡</span> 快速輸入按鈕設定</h2>
                        <span class="qbs-modal-close" title="關閉">×</span>
                    </div>
                    <div class="qbs-modal-body"><div id="qbs-config-list"></div></div>
                    <div class="qbs-modal-footer">
                        <button id="qbs-add-new-btn" class="qbs-modal-btn qbs-modal-btn-secondary">＋ 新增</button>
                        <button id="qbs-save-btn" class="qbs-modal-btn qbs-modal-btn-primary">💾 儲存並關閉</button>
                    </div>
                </div>
            </div>`;
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        console.log('[Modal] createSettingsModal: Modal HTML inserted');

        // 立即綁定事件，確保關閉功能可用
        setTimeout(() => {
            const modal = document.getElementById('quick-button-settings-modal');
            if (modal) {
                const closeBtn = modal.querySelector('.qbs-modal-close');
                if (closeBtn) {
                    closeBtn.addEventListener('click', (e) => {
                        console.log('[Modal] Close button clicked (direct binding)');
                        e.preventDefault();
                        e.stopPropagation();
                        modal.style.display = 'none';
                        console.log('[Modal] Modal closed via direct binding');
                    });
                    console.log('[Modal] Direct close event bound in createSettingsModal');
                }
            }
        }, 10);

        console.log('[Modal] createSettingsModal: Modal created and events bound');
    }

    function openSettingsModal() {
        console.log('[Modal] openSettingsModal: Starting...');

        // 確保模態視窗存在
        if (!document.getElementById('quick-button-settings-modal')) {
            console.log('[Modal] Creating new modal');
            createSettingsModal();
        }

        const modal = document.getElementById('quick-button-settings-modal');
        if (!modal) {
            console.error('[Modal] openSettingsModal: Modal not found after creation');
            return;
        }

        // 強制每次打開都 apply 一次 CSS，確保樣式正確
        applyStyles('');
        console.log('[Modal] Styles applied');

        // 等待 DOM 更新後再綁定事件
        setTimeout(() => {
            initializeModalEvents(); // 每次打開都重新綁定事件，確保 X 可用
            console.log('[Modal] Events initialized');
        }, 50);

        console.log('[Modal] openSettingsModal: called', modal);

        const config = getButtonConfig();
        const configListDiv = modal.querySelector('#qbs-config-list');
        if (configListDiv) {
            configListDiv.innerHTML = '';
            config.forEach(item => addConfigRow(item));
            console.log('[Modal] Config rows added');
        } else {
            console.error('[Modal] Config list div not found');
        }

        modal.style.display = 'flex';
        modal.style.zIndex = '2000';
        modal.style.position = 'fixed';
        modal.style.inset = '0';
        console.log('[Modal] openSettingsModal: modal.style.display =', modal.style.display);
    }

    // (FIX) Bind modal events - allow re-binding to ensure events work
    function initializeModalEvents() {
         const modal = document.getElementById('quick-button-settings-modal');
         if (!modal) {
             console.error('[Modal] initializeModalEvents: Modal not found');
             return;
         }

         // 移除舊的事件監聽器標記，允許重新綁定
         console.log('[Modal] initializeModalEvents: Starting event binding');

         const closeBtn = modal.querySelector('.qbs-modal-close');
         const saveBtn = modal.querySelector('#qbs-save-btn');
         const addBtn = modal.querySelector('#qbs-add-new-btn');

         console.log('[Modal] Found elements:', { closeBtn: !!closeBtn, saveBtn: !!saveBtn, addBtn: !!addBtn });

         if (closeBtn) {
             // 移除可能存在的舊事件監聽器
             closeBtn.replaceWith(closeBtn.cloneNode(true));
             const newCloseBtn = modal.querySelector('.qbs-modal-close');

             newCloseBtn.addEventListener('click', (e) => {
                 console.log('[Modal] Close button clicked');
                 e.preventDefault();
                 e.stopPropagation();
                 modal.style.display = 'none';
                 console.log('[Modal] close: set display none');
             });
             console.log('[Modal] Close button event bound');
         } else {
             console.error('[Modal] Close button not found');
         }

         if (saveBtn) {
             saveBtn.replaceWith(saveBtn.cloneNode(true));
             const newSaveBtn = modal.querySelector('#qbs-save-btn');
             newSaveBtn.addEventListener('click', saveAndCloseModal);
             console.log('[Modal] Save button event bound');
         }

         if (addBtn) {
             addBtn.replaceWith(addBtn.cloneNode(true));
             const newAddBtn = modal.querySelector('#qbs-add-new-btn');
             newAddBtn.addEventListener('click', () => addConfigRow());
             console.log('[Modal] Add button event bound');
         }

         // 點擊背景關閉模態視窗
         modal.addEventListener('click', (e) => {
             if (e.target === modal) {
                 console.log('[Modal] Background clicked');
                 modal.style.display = 'none';
                 console.log('[Modal] click overlay: set display none');
             }
         });

         console.log('[Modal] initializeModalEvents: All events bound successfully');
    }

    function addConfigRow(item = { label: '', text: '' }) {
        const configListDiv = document.getElementById('qbs-config-list');
        if (!configListDiv) return;

        const row = document.createElement('div');
        row.className = 'qbs-config-row';
        row.innerHTML = `
            <input type="text" class="qbs-modal-input" placeholder="按鈕文字 (Label)" value="${item.label.replace(/"/g, '"')}">
            <input type="text" class="qbs-modal-input" placeholder="輸入內容 (Text)" value="${item.text.replace(/"/g, '"')}">
            <button class="qbs-modal-delete-btn" title="刪除此項">×</button>`;

        const deleteBtn = row.querySelector('.qbs-modal-delete-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => row.remove());
        }

        configListDiv.appendChild(row);
    }

    function saveAndCloseModal() {
        const newConfig = [];
        document.querySelectorAll('#qbs-config-list .qbs-config-row').forEach(row => {
            const inputs = row.querySelectorAll('input');
            const label = inputs[0].value.trim();
            const text = inputs[1].value;
            if (label) { newConfig.push({ label, text }); }
        });
        saveButtonConfig(newConfig);
        document.getElementById('quick-button-settings-modal').style.display = 'none';
        document.querySelectorAll('.quick-input-button-container').forEach(c => c.remove());
        addQuickInputButtons();
        alert('設定已儲存！');
    }

    function addQuickInputButtons() {
        const bookingGroups = document.querySelectorAll('.bookignPeriodGroup');
        if (bookingGroups.length === 0) return;
        const quickButtons = getButtonConfig();
        bookingGroups.forEach(group => {
            const headerDiv = group.querySelector('div[style*="background-color:#e4ece0"]');
            if (!headerDiv || !headerDiv.textContent.includes('Meeting Name')) return;
            if (headerDiv.querySelector('.quick-input-button-container')) return;

            const buttonContainer = document.createElement('div');
            buttonContainer.className = 'quick-input-button-container';
            buttonContainer.style.cssText = 'float: left; margin-left: 15px; margin-top: 4px;';
            quickButtons.forEach(btnInfo => {
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'btn btn-info btn-sm';
                button.textContent = btnInfo.label;
                button.style.marginLeft = '5px';
                button.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const inputBox = group.querySelector('input.bookingtitle');
                    if (inputBox) {
                        inputBox.value += btnInfo.text;
                        inputBox.dispatchEvent(new Event('input', { bubbles: true }));
                        inputBox.focus();
                    }
                });
                buttonContainer.appendChild(button);
            });
            const settingsButton = document.createElement('button');
            settingsButton.type = 'button';
            settingsButton.className = 'btn btn-secondary btn-sm';
            settingsButton.innerHTML = '⚙️';
            settingsButton.title = '編輯快速按鈕';
            settingsButton.style.marginLeft = '10px';
            settingsButton.addEventListener('click', (e) => {
                e.stopPropagation();
                console.log('[Modal] settingsButton clicked');
                openSettingsModal();
            });
            buttonContainer.appendChild(settingsButton);
            headerDiv.appendChild(buttonContainer);
        });
    }

    function applyStyles(css) {
        // 將 style id 改為唯一，並每次都覆蓋內容
        const styleId = 'vtc-ibooking-modal-style';
        let styleTag = document.getElementById(styleId);
        const modernModalCss = `
#quick-button-settings-modal.qbs-modal-overlay { position: fixed !important; top: 0 !important; left: 0 !important; width: 100vw !important; height: 100vh !important; background: rgba(30,40,60,0.35) !important; z-index: 99999 !important; display: flex !important; align-items: center !important; justify-content: center !important; transition: background 0.2s !important; }
#quick-button-settings-modal .qbs-modal-box { background: #fff !important; padding: 32px 28px 22px 28px !important; border-radius: 18px !important; box-shadow: 0 8px 32px rgba(0,0,0,0.18) !important; width: 98vw !important; max-width: 540px !important; min-width: 340px !important; border: 1.5px solid #e0e6ef !important; position: relative !important; animation: qbs-fadein 0.25s !important; }
@keyframes qbs-fadein { from { opacity: 0; transform: translateY(30px);} to { opacity: 1; transform: none; } }
#quick-button-settings-modal .qbs-modal-header { display: flex !important; justify-content: space-between !important; align-items: center !important; border-bottom: 1px solid #f0f0f0 !important; padding-bottom: 10px !important; margin-bottom: 18px !important; }
#quick-button-settings-modal .qbs-modal-header h2 { margin: 0 !important; font-size: 1.35rem !important; color: #222 !important; font-weight: 700 !important; letter-spacing: 1px !important; }
#quick-button-settings-modal .qbs-modal-close { font-size: 1.7rem !important; font-weight: bold !important; cursor: pointer !important; color: #b0b0b0 !important; transition: color 0.2s !important; }
#quick-button-settings-modal .qbs-modal-close:hover { color: #007bff !important; }
#quick-button-settings-modal .qbs-modal-body { max-height: 48vh !important; overflow-y: auto !important; margin-bottom: 10px !important; }
#quick-button-settings-modal .qbs-config-row { display: flex !important; align-items: center !important; margin-bottom: 12px !important; }
#quick-button-settings-modal .qbs-modal-input { flex-grow: 1 !important; margin-right: 10px !important; padding: 9px 10px !important; border: 1.2px solid #cfd8dc !important; border-radius: 6px !important; font-size: 1rem !important; background: #f8fafc !important; transition: border 0.2s !important; min-width: 0 !important; }
#quick-button-settings-modal .qbs-modal-input:focus { border: 1.2px solid #007bff !important; outline: none !important; background: #fff !important; }
#quick-button-settings-modal .qbs-modal-delete-btn { background: #f44336 !important; color: white !important; border: none !important; border-radius: 50% !important; width: 32px !important; height: 32px !important; cursor: pointer !important; font-weight: bold !important; line-height: 32px !important; text-align: center !important; font-size: 1.2rem !important; margin-left: 2px !important; transition: background 0.2s !important; flex-shrink: 0 !important; }
#quick-button-settings-modal .qbs-modal-delete-btn:hover { background: #d32f2f !important; }
#quick-button-settings-modal .qbs-modal-footer { border-top: 1px solid #f0f0f0 !important; padding-top: 15px !important; margin-top: 18px !important; text-align: right !important; }
#quick-button-settings-modal .qbs-modal-btn { padding: 9px 18px !important; border: none !important; border-radius: 6px !important; cursor: pointer !important; font-size: 1rem !important; font-weight: 500 !important; transition: background 0.2s, color 0.2s !important; }
#quick-button-settings-modal .qbs-modal-btn-primary { background: linear-gradient(90deg,#007bff 60%,#0056b3 100%) !important; color: white !important; }
#quick-button-settings-modal .qbs-modal-btn-primary:hover { background: #0056b3 !important; }
#quick-button-settings-modal .qbs-modal-btn-secondary { background: #e3e6ef !important; color: #222 !important; margin-right: 10px !important; }
#quick-button-settings-modal .qbs-modal-btn-secondary:hover { background: #d1d8e6 !important; }
        `;
        if (!styleTag) {
            styleTag = document.createElement('style');
            styleTag.id = styleId;
            document.head.appendChild(styleTag);
        }
        styleTag.innerHTML = css + modernModalCss;
    }

    // This is the full, correct version of the function
    function replaceOrAppendSubject() {
        setTimeout(() => {
            const bookingBoxes = document.querySelectorAll('.period-box[data-original-title]:not([data-text-fixed])');
            if (bookingBoxes.length === 0) return;
            bookingBoxes.forEach(box => {
                const titleData = box.getAttribute('data-original-title');
                const targetDiv = box.querySelector('div[style*="background-color"]');
                if (!titleData || !targetDiv) return;
                const subjectMatch = titleData.match(/Subject:\s*(.*?)\s*<br>/i);
                const subjectText = subjectMatch ? subjectMatch[1].trim() : null;
                const organizerMatch = titleData.match(/Organizer:\s*(.*?)\s*(?:<br>|<!--)/i);
                const organizerText = organizerMatch ? organizerMatch[1].trim() : null;
                if (subjectText) {
                    const originalHTML = targetDiv.innerHTML;
                    if (organizerText && originalHTML.includes(organizerText)) {
                        const newHTML = originalHTML.replace(organizerText, subjectText);
                        targetDiv.innerHTML = newHTML;
                    } else {
                        targetDiv.innerHTML += ` ${subjectText}`;
                    }
                    box.setAttribute('data-text-fixed', 'true');
                }
            });
        }, 100);
    }

    function adjustLayout() {
        const weekViewContainer = document.getElementById('calendarBody_month');
        const dayViewContainer = document.querySelector('#dayCalendar > div[style*="height"]');
        const targetContainer = weekViewContainer || dayViewContainer;
        if (!targetContainer) { return; }
        const offsetTop = targetContainer.getBoundingClientRect().top;
        const newContainerHeight = `calc(100vh - ${offsetTop}px - 15px)`;
        const cssRules = `
            .container-fluid { height: 95vh !important; overflow: hidden !important; }
            #calendarBody_month, #dayCalendar > div[style*="height"] { height: ${newContainerHeight} !important; overflow-y: auto !important; }
            .calendar-column-shadow { border-right: 1px solid #e0e0e0 !important; }
            #resourceMark { max-height: unset !important; overflow-y: visible !important; }
            .qbs-modal-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center; }
            .qbs-modal-content { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 5px 15px rgba(0,0,0,0.3); width: 90%; max-width: 600px; }
            .qbs-modal-header { display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #eee; padding-bottom: 10px; margin-bottom: 15px; }
            .qbs-modal-header h2 { margin: 0; font-size: 1.2rem; }
            .qbs-modal-close { font-size: 1.5rem; font-weight: bold; cursor: pointer; color: #aaa; }
            .qbs-modal-close:hover { color: #000; }
            .qbs-modal-body { max-height: 60vh; overflow-y: auto; }
            .qbs-config-row { display: flex; align-items: center; margin-bottom: 10px; }
            .qbs-input { flex-grow: 1; margin-right: 10px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; font-size: 0.9rem; }
            .qbs-delete-btn { background: #f44336; color: white; border: none; border-radius: 50%; width: 24px; height: 24px; cursor: pointer; font-weight: bold; line-height: 24px; text-align: center; }
            .qbs-modal-footer { border-top: 1px solid #eee; padding-top: 15px; margin-top: 15px; text-align: right; }
            .qbs-btn { padding: 8px 15px; border: none; border-radius: 4px; cursor: pointer; font-size: 0.9rem; }
            .qbs-btn-primary { background: #007bff; color: white; }
            .qbs-btn-secondary { background: #6c757d; color: white; margin-right: 10px; }
        `;
        applyStyles(cssRules);
    }

    function runAllUpdates() {
        if (document.getElementById('userCalendar')) {
            adjustLayout();
            replaceOrAppendSubject();
        }
        if (document.querySelector('.bookignPeriodGroup')) {
            addQuickInputButtons();
        }
    }

    // --- SCRIPT INITIALIZATION ---
    function initialize() {
        // 不在初始化時創建模態視窗，只在需要時才創建
        // createSettingsModal();
        // initializeModalEvents(); // Bind events to the modal controls

        const observer = new MutationObserver(runAllUpdates);
        observer.observe(document.body, { childList: true, subtree: true });

        // Initial run
        runAllUpdates();
    }

    // Start everything after the page is fully loaded
    window.addEventListener('load', initialize);

})();
